import type { NLFormItemValue, NLProcessInfo, NLProcessSubmitParams, ProcessTaskDraft } from "#/entity/process";
import type { NLFormComponent } from "#/entity/process/nl-form-components";
import { createContext } from "react";
import type { FormProps } from 'antd-mobile'
import type { FormReactionState } from '../hooks/use-nl-form-reaction';

interface NLFormContextState {
  /**
   * antd 的 form 实例
   */
  form: FormProps['form'] | null;
  /**
   * 流程基本信息
   */
  processInfo: NLProcessInfo,
  onSubmit?: (params: NLProcessSubmitParams) => void;
  onSaveDraft?: (params: NLProcessSubmitParams) => void;
  draft?: ProcessTaskDraft;
  /**
   * 表单组件列表
   */
  formComponents?: NLFormComponent[];
  /**
   * 表单值
   */
  formValues?: Record<string, NLFormItemValue>;
  /**
   * 联动状态
   */
  reactionStates?: Record<string, FormReactionState>;
  /**
   * 更新表单值的回调
   */
  onFormValuesChange?: (changedValues: any, allValues: any) => void;
}

export const NLFormContext = createContext<NLFormContextState>({
  form: null,
  processInfo: {
    channelPath: '',
    color: '',
    description: '',
    icon: '',
    channelUuid: '',
    priorityUuid: '',
  },
  formComponents: [],
  formValues: {},
  reactionStates: {},
  onFormValuesChange: () => {}
})
