/**
 * 表单联动工具函数
 */

/**
 * 表达式类型枚举
 */
export enum ReactionExpression {
  EQUAL = 'equal',
  NOT_EQUAL = 'notequal',
  IS_NULL = 'isnull',
  IS_NOT_NULL = 'isnotnull',
  INCLUDE = 'include',
  EXCLUDE = 'exclude',
  CHANGE = 'change',
  GREATER_THAN = 'greaterthan',
  LESS_THAN = 'lessthan',
  GREATER_EQUAL = 'greaterequal',
  LESS_EQUAL = 'lessequal'
}

/**
 * 关系类型枚举
 */
export enum ReactionRelation {
  AND = 'and',
  OR = 'or'
}

/**
 * 标准化值，处理不同类型的数据
 */
export function normalizeValue(value: any): any {
  if (value === null || value === undefined) {
    return null;
  }
  
  if (typeof value === 'string' && value.trim() === '') {
    return null;
  }
  
  if (Array.isArray(value)) {
    if (value.length === 0) {
      return null;
    }
    return value.map(normalizeValue).filter(v => v !== null);
  }
  
  if (typeof value === 'object' && value.value !== undefined) {
    return normalizeValue(value.value);
  }
  
  return value;
}

/**
 * 比较两个值是否相等
 */
export function isEqual(value1: any, value2: any): boolean {
  const normalized1 = normalizeValue(value1);
  const normalized2 = normalizeValue(value2);
  
  if (normalized1 === null && normalized2 === null) {
    return true;
  }
  
  if (normalized1 === null || normalized2 === null) {
    return false;
  }
  
  if (Array.isArray(normalized1) && Array.isArray(normalized2)) {
    if (normalized1.length !== normalized2.length) {
      return false;
    }
    return normalized1.every((v, i) => isEqual(v, normalized2[i]));
  }
  
  if (Array.isArray(normalized1) || Array.isArray(normalized2)) {
    return false;
  }
  
  return String(normalized1) === String(normalized2);
}

/**
 * 检查值是否为空
 */
export function isEmpty(value: any): boolean {
  const normalized = normalizeValue(value);
  return normalized === null;
}

/**
 * 检查值是否包含指定内容
 */
export function includes(value: any, searchValue: any): boolean {
  const normalized = normalizeValue(value);
  const normalizedSearch = normalizeValue(searchValue);
  
  if (normalized === null || normalizedSearch === null) {
    return false;
  }
  
  if (Array.isArray(normalized)) {
    return normalized.some(v => includes(v, normalizedSearch));
  }
  
  if (Array.isArray(normalizedSearch)) {
    return normalizedSearch.some(v => includes(normalized, v));
  }
  
  return String(normalized).includes(String(normalizedSearch));
}

/**
 * 数值比较
 */
export function compareNumbers(value1: any, value2: any): number {
  const num1 = Number.parseFloat(String(value1));
  const num2 = Number.parseFloat(String(value2));
  
  if (Number.isNaN(num1) || Number.isNaN(num2)) {
    // 如果不是数字，按字符串比较
    return String(value1).localeCompare(String(value2));
  }
  
  return num1 - num2;
}

/**
 * 执行表达式判断
 */
export function executeExpression(
  expression: string,
  currentValue: any,
  compareValues: any[]
): boolean {
  const normalized = normalizeValue(currentValue);
  const normalizedCompareValues = compareValues.map(normalizeValue);
  
  switch (expression) {
    case ReactionExpression.EQUAL:
      return normalizedCompareValues.some(val => isEqual(normalized, val));
      
    case ReactionExpression.NOT_EQUAL:
      return !normalizedCompareValues.some(val => isEqual(normalized, val));
      
    case ReactionExpression.IS_NULL:
      return isEmpty(currentValue);
      
    case ReactionExpression.IS_NOT_NULL:
      return !isEmpty(currentValue);
      
    case ReactionExpression.INCLUDE:
      return normalizedCompareValues.some(val => includes(normalized, val));
      
    case ReactionExpression.EXCLUDE:
      return !normalizedCompareValues.some(val => includes(normalized, val));
      
    case ReactionExpression.CHANGE:
      // change表达式通常用于触发联动，判断是否有值变化
      return !isEmpty(currentValue);
      
    case ReactionExpression.GREATER_THAN:
      return normalizedCompareValues.some(val => compareNumbers(normalized, val) > 0);
      
    case ReactionExpression.LESS_THAN:
      return normalizedCompareValues.some(val => compareNumbers(normalized, val) < 0);
      
    case ReactionExpression.GREATER_EQUAL:
      return normalizedCompareValues.some(val => compareNumbers(normalized, val) >= 0);
      
    case ReactionExpression.LESS_EQUAL:
      return normalizedCompareValues.some(val => compareNumbers(normalized, val) <= 0);
      
    default:
      console.warn(`未知的表达式类型: ${expression}`);
      return false;
  }
}

/**
 * 组合多个条件结果
 */
export function combineResults(
  results: boolean[],
  relations: string[]
): boolean {
  if (results.length === 0) {
    return false;
  }
  
  if (results.length === 1) {
    return results[0];
  }
  
  let combinedResult = results[0];
  
  for (let i = 1; i < results.length; i++) {
    const relation = relations[i - 1] || ReactionRelation.AND;
    
    if (relation === ReactionRelation.AND) {
      combinedResult = combinedResult && results[i];
    } else if (relation === ReactionRelation.OR) {
      combinedResult = combinedResult || results[i];
    }
  }
  
  return combinedResult;
}

/**
 * 调试工具：打印联动执行过程
 */
export function debugReaction(
  componentUuid: string,
  reactionType: string,
  conditionResults: any[],
  finalResult: boolean
): void {
  if (process.env.NODE_ENV === 'development') {
    console.group(`🔗 联动调试 - ${componentUuid} - ${reactionType}`);
    console.log('条件结果:', conditionResults);
    console.log('最终结果:', finalResult);
    console.groupEnd();
  }
}

/**
 * 验证联动配置的有效性
 */
export function validateReactionConfig(reaction: any): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];
  
  if (!reaction) {
    return { isValid: true, errors: [] };
  }
  
  // 检查conditionGroupList
  if (reaction.conditionGroupList && !Array.isArray(reaction.conditionGroupList)) {
    errors.push('conditionGroupList必须是数组');
  }
  
  // 检查conditionGroupRelList
  if (reaction.conditionGroupRelList && !Array.isArray(reaction.conditionGroupRelList)) {
    errors.push('conditionGroupRelList必须是数组');
  }
  
  // 检查每个条件组
  if (reaction.conditionGroupList) {
    reaction.conditionGroupList.forEach((group: any, index: number) => {
      if (!group.conditionList || !Array.isArray(group.conditionList)) {
        errors.push(`条件组${index}的conditionList必须是数组`);
      }
      
      if (group.conditionRelList && !Array.isArray(group.conditionRelList)) {
        errors.push(`条件组${index}的conditionRelList必须是数组`);
      }
      
      // 检查每个条件
      if (group.conditionList) {
        group.conditionList.forEach((condition: any, condIndex: number) => {
          if (!condition.formItemUuid) {
            errors.push(`条件组${index}的条件${condIndex}缺少formItemUuid`);
          }
          
          if (!condition.expression) {
            errors.push(`条件组${index}的条件${condIndex}缺少expression`);
          }
        });
      }
    });
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}
