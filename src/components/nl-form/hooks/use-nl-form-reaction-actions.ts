import { useCallback } from 'react';
import type { 
  NLFormComponent, 
  NLFormReactionRule,
  NLFormReactionSetValueRule,
} from '#/entity/process/nl-form-components';

/**
 * 联动动作类型
 */
export enum ReactionActionType {
  HIDE = 'hide',
  DISPLAY = 'display',
  DISABLE = 'disable',
  READONLY = 'readonly',
  REQUIRED = 'required',
  MASK = 'mask',
  CLEAR_VALUE = 'clearValue',
  SET_VALUE = 'setvalue',
  SET_VALUE_OTHER = 'setValueOther',
  FILTER = 'filter',
  EMIT = 'emit'
}

/**
 * 联动动作执行结果
 */
export interface ReactionActionResult {
  type: ReactionActionType;
  componentUuid: string;
  executed: boolean;
  value?: any;
  error?: string;
}

/**
 * 联动动作执行器Hook
 */
export function useNLFormReactionActions(
  evaluateReactionRule: (rule: NLFormReactionRule) => boolean,
  getFormValue: (uuid: string) => any
) {

  /**
   * 执行隐藏动作
   */
  const executeHideAction = useCallback((
    component: NLFormComponent,
    rule: NLFormReactionRule
  ): ReactionActionResult => {
    try {
      const shouldHide = evaluateReactionRule(rule);
      return {
        type: ReactionActionType.HIDE,
        componentUuid: component.uuid,
        executed: true,
        value: shouldHide
      };
    } catch (error) {
      return {
        type: ReactionActionType.HIDE,
        componentUuid: component.uuid,
        executed: false,
        error: error instanceof Error ? error.message : '执行隐藏动作失败'
      };
    }
  }, [evaluateReactionRule]);

  /**
   * 执行显示动作
   */
  const executeDisplayAction = useCallback((
    component: NLFormComponent,
    rule: NLFormReactionRule
  ): ReactionActionResult => {
    try {
      const shouldDisplay = evaluateReactionRule(rule);
      return {
        type: ReactionActionType.DISPLAY,
        componentUuid: component.uuid,
        executed: true,
        value: shouldDisplay
      };
    } catch (error) {
      return {
        type: ReactionActionType.DISPLAY,
        componentUuid: component.uuid,
        executed: false,
        error: error instanceof Error ? error.message : '执行显示动作失败'
      };
    }
  }, [evaluateReactionRule]);

  /**
   * 执行禁用动作
   */
  const executeDisableAction = useCallback((
    component: NLFormComponent,
    rule: NLFormReactionRule
  ): ReactionActionResult => {
    try {
      const shouldDisable = evaluateReactionRule(rule);
      return {
        type: ReactionActionType.DISABLE,
        componentUuid: component.uuid,
        executed: true,
        value: shouldDisable
      };
    } catch (error) {
      return {
        type: ReactionActionType.DISABLE,
        componentUuid: component.uuid,
        executed: false,
        error: error instanceof Error ? error.message : '执行禁用动作失败'
      };
    }
  }, [evaluateReactionRule]);

  /**
   * 执行只读动作
   */
  const executeReadonlyAction = useCallback((
    component: NLFormComponent,
    rule: NLFormReactionRule
  ): ReactionActionResult => {
    try {
      const shouldReadonly = evaluateReactionRule(rule);
      return {
        type: ReactionActionType.READONLY,
        componentUuid: component.uuid,
        executed: true,
        value: shouldReadonly
      };
    } catch (error) {
      return {
        type: ReactionActionType.READONLY,
        componentUuid: component.uuid,
        executed: false,
        error: error instanceof Error ? error.message : '执行只读动作失败'
      };
    }
  }, [evaluateReactionRule]);

  /**
   * 执行必填动作
   */
  const executeRequiredAction = useCallback((
    component: NLFormComponent,
    rule: NLFormReactionRule
  ): ReactionActionResult => {
    try {
      const shouldRequired = evaluateReactionRule(rule);
      return {
        type: ReactionActionType.REQUIRED,
        componentUuid: component.uuid,
        executed: true,
        value: shouldRequired
      };
    } catch (error) {
      return {
        type: ReactionActionType.REQUIRED,
        componentUuid: component.uuid,
        executed: false,
        error: error instanceof Error ? error.message : '执行必填动作失败'
      };
    }
  }, [evaluateReactionRule]);

  /**
   * 执行遮罩动作
   */
  const executeMaskAction = useCallback((
    component: NLFormComponent,
    rule: NLFormReactionRule
  ): ReactionActionResult => {
    try {
      const shouldMask = evaluateReactionRule(rule);
      return {
        type: ReactionActionType.MASK,
        componentUuid: component.uuid,
        executed: true,
        value: shouldMask
      };
    } catch (error) {
      return {
        type: ReactionActionType.MASK,
        componentUuid: component.uuid,
        executed: false,
        error: error instanceof Error ? error.message : '执行遮罩动作失败'
      };
    }
  }, [evaluateReactionRule]);

  /**
   * 执行清空值动作
   */
  const executeClearValueAction = useCallback((
    component: NLFormComponent,
    rule: NLFormReactionRule
  ): ReactionActionResult => {
    try {
      const shouldClear = evaluateReactionRule(rule);
      return {
        type: ReactionActionType.CLEAR_VALUE,
        componentUuid: component.uuid,
        executed: true,
        value: shouldClear
      };
    } catch (error) {
      return {
        type: ReactionActionType.CLEAR_VALUE,
        componentUuid: component.uuid,
        executed: false,
        error: error instanceof Error ? error.message : '执行清空值动作失败'
      };
    }
  }, [evaluateReactionRule]);

  /**
   * 执行设值动作
   */
  const executeSetValueAction = useCallback((
    component: NLFormComponent,
    rule: NLFormReactionSetValueRule
  ): ReactionActionResult => {
    try {
      const shouldSetValue = evaluateReactionRule(rule);
      
      if (!shouldSetValue) {
        return {
          type: ReactionActionType.SET_VALUE,
          componentUuid: component.uuid,
          executed: true,
          value: null
        };
      }

      let setValue: any = null;

      if (rule.type === 'static') {
        setValue = rule.value;
      } else if (rule.type === 'dynamic' && rule.value) {
        // 动态赋值：从其他字段获取值
        const [sourceUuid, sourceField = 'value'] = rule.value.split('#');
        const sourceValue = getFormValue(sourceUuid);
        
        if (sourceValue && typeof sourceValue === 'object') {
          setValue = sourceValue[sourceField];
        } else {
          setValue = sourceValue;
        }
      }

      return {
        type: ReactionActionType.SET_VALUE,
        componentUuid: component.uuid,
        executed: true,
        value: setValue
      };
    } catch (error) {
      return {
        type: ReactionActionType.SET_VALUE,
        componentUuid: component.uuid,
        executed: false,
        error: error instanceof Error ? error.message : '执行设值动作失败'
      };
    }
  }, [evaluateReactionRule, getFormValue]);

  /**
   * 执行所有联动动作
   */
  const executeAllActions = useCallback((
    component: NLFormComponent
  ): ReactionActionResult[] => {
    const results: ReactionActionResult[] = [];
    const reaction = component.reaction;

    if (!reaction) {
      return results;
    }

    // 执行各种联动动作
    if (reaction.hide) {
      results.push(executeHideAction(component, reaction.hide));
    }

    if (reaction.display) {
      results.push(executeDisplayAction(component, reaction.display));
    }

    if (reaction.disable) {
      results.push(executeDisableAction(component, reaction.disable));
    }

    if (reaction.readonly) {
      results.push(executeReadonlyAction(component, reaction.readonly));
    }

    if (reaction.required) {
      results.push(executeRequiredAction(component, reaction.required));
    }

    if (reaction.mask) {
      results.push(executeMaskAction(component, reaction.mask));
    }

    if (reaction.clearValue) {
      results.push(executeClearValueAction(component, reaction.clearValue));
    }

    if (reaction.setvalue) {
      results.push(executeSetValueAction(component, reaction.setvalue));
    }

    return results;
  }, [
    executeHideAction,
    executeDisplayAction,
    executeDisableAction,
    executeReadonlyAction,
    executeRequiredAction,
    executeMaskAction,
    executeClearValueAction,
    executeSetValueAction
  ]);

  return {
    executeHideAction,
    executeDisplayAction,
    executeDisableAction,
    executeReadonlyAction,
    executeRequiredAction,
    executeMaskAction,
    executeClearValueAction,
    executeSetValueAction,
    executeAllActions
  };
}
