import { useCallback, useMemo } from 'react';
import type { 
  NLFormComponent, 
  NLFormReactionRule, 
  NLFormReactionCondition,
  NLFormReactionConditionGroup,
} from '#/entity/process/nl-form-components';
import type { NLFormItemValue } from '#/entity/process';

/**
 * 表单联动状态
 */
export interface FormReactionState {
  isHidden: boolean;      // 是否隐藏
  isDisabled: boolean;    // 是否禁用
  isRequired: boolean;    // 是否必填
  isMasked: boolean;      // 是否遮罩
  isReadonly: boolean;    // 是否只读
  shouldClearValue: boolean; // 是否需要清空值
}

function isEmptyObject(obj: any): boolean {
  return Object.keys(obj).length === 0;
}

/**
 * 表单联动Hook
 * @param formComponents 表单组件列表
 * @param formValues 表单值
 */
export function useNLFormReaction(
  formComponents: NLFormComponent[],
  formValues: Record<string, NLFormItemValue>
) {
  
  /**
   * 获取表单组件的值
   */
  const getFormValue = useCallback((uuid: string): any => {
    const value = formValues[uuid];
    if (!value) return null;

    // 根据不同的组件类型返回不同的值格式
    if (Array.isArray(value.dataList)) {
      if (value.dataList.length === 0) return null;
      if (value.dataList.length === 1) {
        // @ts-ignore
        return value.dataList[0]?.value || value.dataList[0];
      }
      // @ts-ignore
      return value.dataList.map(item => item?.value || item);
    }
    // @ts-ignore
    return value.dataList?.value || value.dataList;
  }, [formValues]);

  /**
   * 判断单个条件是否满足
   */
  const evaluateCondition = useCallback((
    condition: NLFormReactionCondition
  ): boolean => {
    const { formItemUuid, expression, valueList = [] } = condition;
    const currentValue = getFormValue(formItemUuid);
    
    switch (expression) {
      case 'equal':
        if (Array.isArray(currentValue)) {
          return valueList.some(val => currentValue.includes(val));
        }
        return valueList.includes(currentValue);
        
      case 'notequal':
        if (Array.isArray(currentValue)) {
          return !valueList.some(val => currentValue.includes(val));
        }
        return !valueList.includes(currentValue);
        
      case 'isnull':
        return currentValue === null || currentValue === undefined || currentValue === '';
        
      case 'isnotnull':
        return currentValue !== null && currentValue !== undefined && currentValue !== '';
        
      case 'include':
        if (Array.isArray(currentValue)) {
          return valueList.every(val => currentValue.includes(val));
        }
        return valueList.some(val => String(currentValue).includes(String(val)));
        
      case 'exclude':
        if (Array.isArray(currentValue)) {
          return !valueList.some(val => currentValue.includes(val));
        }
        return !valueList.some(val => String(currentValue).includes(String(val)));
        
      case 'change':
        // change表达式通常用于触发联动，这里简单判断是否有值
        return currentValue !== null && currentValue !== undefined && currentValue !== '';
        
      default:
        console.warn(`未知的表达式类型: ${expression}`);
        return false;
    }
  }, [getFormValue]);

  /**
   * 判断条件组是否满足
   */
  const evaluateConditionGroup = useCallback((
    conditionGroup: NLFormReactionConditionGroup
  ): boolean => {
    const { conditionList, conditionRelList } = conditionGroup;
    
    if (!conditionList || conditionList.length === 0) {
      return false;
    }
    
    // 如果只有一个条件，直接返回结果
    if (conditionList.length === 1) {
      return evaluateCondition(conditionList[0]);
    }
    
    // 多个条件时，根据关系进行组合
    let result = evaluateCondition(conditionList[0]);
    
    for (let i = 1; i < conditionList.length; i++) {
      const conditionResult = evaluateCondition(conditionList[i]);
      const relation = conditionRelList[i - 1] || 'and';
      
      if (relation === 'and') {
        result = result && conditionResult;
      } else if (relation === 'or') {
        result = result || conditionResult;
      }
    }
    
    return result;
  }, [evaluateCondition]);

  /**
   * 判断联动规则是否满足
   */
  const evaluateReactionRule = useCallback((
    rule: NLFormReactionRule
  ): boolean => {
    const { conditionGroupList, conditionGroupRelList } = rule;
    
    if (!conditionGroupList || conditionGroupList.length === 0) {
      return false;
    }
    
    // 如果只有一个条件组，直接返回结果
    if (conditionGroupList.length === 1) {
      return evaluateConditionGroup(conditionGroupList[0]);
    }
    
    // 多个条件组时，根据关系进行组合
    let result = evaluateConditionGroup(conditionGroupList[0]);
    for (let i = 1; i < conditionGroupList.length; i++) {
      const groupResult = evaluateConditionGroup(conditionGroupList[i]);
      const relation = conditionGroupRelList[i - 1] || 'and';
      
      if (relation === 'and') {
        result = result && groupResult;
      } else if (relation === 'or') {
        result = result || groupResult;
      }
    }
    
    return result;
  }, [evaluateConditionGroup]);

  /**
   * 计算组件的联动状态
   */
  const getComponentReactionState = useCallback((
    component: NLFormComponent
  ): FormReactionState => {
    const reaction = component.reaction;
    const overrideConfig = component.override_config || {};
    
    if (!reaction) {
      return {
        isHidden: overrideConfig.isHide || component.config?.isHide || false,
        isDisabled: overrideConfig.isDisabled || component.config?.isDisabled || false,
        isRequired: overrideConfig.isRequired || component.config?.isRequired || false,
        isMasked: overrideConfig.isMask || component.config?.isMask || false,
        isReadonly: overrideConfig.isReadOnly || component.config?.isReadOnly || false,
        shouldClearValue: false
      };
    }
    
    // 计算各种联动状态
    const isHidden = (reaction.hide && !isEmptyObject(reaction.hide)) ? !evaluateReactionRule(reaction.hide) : 
                    (overrideConfig.isHide || component.config?.isHide || false);
    
    const shouldDisplay = (reaction.display && !isEmptyObject(reaction.display)) ? evaluateReactionRule(reaction.display) : true;
    const finalHidden = isHidden && !shouldDisplay;
    
    const isDisabled = (reaction.disable&& !isEmptyObject(reaction.disable)) ? evaluateReactionRule(reaction.disable) : 
                      (overrideConfig.isDisabled || component.config?.isDisabled || false);
    
    const isRequired = (reaction.required && !isEmptyObject(reaction.required)) ?  evaluateReactionRule(reaction.required) : 
                      (overrideConfig.isRequired || component.config?.isRequired || false);
    
    const isMasked = (reaction.mask && !isEmptyObject(reaction.mask)) ? evaluateReactionRule(reaction.mask) : 
                    (overrideConfig.isMask || component.config?.isMask || false);
    
    const isReadonly = (reaction.readonly && !isEmptyObject(reaction.readonly)) ? evaluateReactionRule(reaction.readonly) : 
                      (overrideConfig.isReadOnly || component.config?.isReadOnly || false);
    
    const shouldClearValue = (reaction.clearValue && !isEmptyObject(reaction.clearValue)) ? evaluateReactionRule(reaction.clearValue) : false;
    
    return {
      isHidden: finalHidden,
      isDisabled,
      isRequired,
      isMasked,
      isReadonly,
      shouldClearValue
    };
  }, [evaluateReactionRule]);

  /**
   * 计算所有组件的联动状态
   */
  const reactionStates = useMemo(() => {
    const states: Record<string, FormReactionState> = {};
    
    formComponents.forEach(component => {
      states[component.uuid] = getComponentReactionState(component);
    });
    
    return states;
  }, [formComponents, getComponentReactionState]);

  /**
   * 获取需要清空值的组件列表
   */
  const getComponentsToClear = useCallback((): string[] => {
    const componentsToClear: string[] = [];

    formComponents.forEach(component => {
      const state = reactionStates[component.uuid];
      if (state?.shouldClearValue) {
        componentsToClear.push(component.uuid);
      }
    });

    return componentsToClear;
  }, [formComponents, reactionStates]);

  /**
   * 获取过滤后的矩阵数据（用于下拉选择等组件的数据过滤）
   */
  const getFilteredMatrixData = useCallback((
    component: NLFormComponent,
    originalData: any[]
  ): any[] => {
    const reaction = component.reaction;
    if (!reaction?.filter) {
      return originalData;
    }

    // 这里可以根据filter规则对数据进行过滤
    // 具体实现需要根据你的filter规则结构来定制
    return originalData;
  }, []);


  return {
    reactionStates,
    getComponentReactionState,
    evaluateReactionRule,
    evaluateConditionGroup,
    evaluateCondition,
    getComponentsToClear,
    getFilteredMatrixData,
  };
}
