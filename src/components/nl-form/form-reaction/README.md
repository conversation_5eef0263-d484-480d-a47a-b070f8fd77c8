# 表单联动功能使用指南

## 概述

表单联动功能允许根据表单字段的值动态控制其他字段的显示、隐藏、必填、禁用等状态。这个功能基于你提供的联动规则数据结构实现。

## 核心组件

### 1. useNLFormReaction Hook

主要的联动逻辑处理Hook，负责：
- 解析联动规则
- 监听表单值变化
- 计算联动状态
- 执行联动动作

```typescript
const { reactionStates, getComponentsToClear, getComponentsToSetValue } = useNLFormReaction(
  formComponents, // 表单组件配置
  formValues      // 当前表单值
);
```

### 2. 联动状态类型

```typescript
interface FormReactionState {
  isHidden: boolean;        // 是否隐藏
  isDisabled: boolean;      // 是否禁用
  isRequired: boolean;      // 是否必填
  isMasked: boolean;        // 是否遮罩
  isReadonly: boolean;      // 是否只读
  shouldClearValue: boolean; // 是否需要清空值
}
```

## 联动规则配置

### 基本结构

```typescript
{
  "reaction": {
    "display": {           // 显示联动
      "conditionGroupList": [...],
      "conditionGroupRelList": ["and", "or"]
    },
    "hide": {...},         // 隐藏联动
    "required": {...},     // 必填联动
    "disable": {...},      // 禁用联动
    "readonly": {...},     // 只读联动
    "mask": {...},         // 遮罩联动
    "clearValue": {...},   // 清空值联动
    "setvalue": {...}      // 设值联动
  }
}
```

### 条件表达式

支持的表达式类型：
- `equal`: 等于
- `notequal`: 不等于
- `isnull`: 为空
- `isnotnull`: 不为空
- `include`: 包含
- `exclude`: 不包含
- `change`: 值改变
- `greaterthan`: 大于
- `lessthan`: 小于
- `greaterequal`: 大于等于
- `lessequal`: 小于等于

### 示例配置

```typescript
{
  "display": {
    "conditionGroupList": [
      {
        "uuid": "group1",
        "conditionList": [
          {
            "uuid": "condition1",
            "formItemUuid": "school_field_uuid",
            "expression": "isnotnull",
            "valueList": []
          },
          {
            "uuid": "condition2", 
            "formItemUuid": "name_field_uuid",
            "expression": "notequal",
            "valueList": ["b"]
          }
        ],
        "conditionRelList": ["and"]  // 条件间用AND连接
      }
    ],
    "conditionGroupRelList": []
  }
}
```

## 使用方法

### 1. 在表单组件中集成

```typescript
import { useContext } from 'react';
import { NLFormContext } from '../nl-form-context';

const MyFormComponent: React.FC<Props> = ({ nlFormComponent }) => {
  const { reactionStates } = useContext(NLFormContext);
  const reactionState = reactionStates?.[nlFormComponent.uuid];

  return (
    <Form.Item
      required={reactionState?.isRequired ?? config.isRequired}
      disabled={reactionState?.isDisabled ?? config.isDisabled}
      hidden={reactionState?.isHidden ?? config.isHide}
      className={classNames({ 
        'blur-sm': reactionState?.isMasked ?? config.isMask,
        'pointer-events-none': reactionState?.isReadonly ?? config.isReadOnly
      })}
    >
      {/* 表单控件 */}
    </Form.Item>
  );
};
```

### 2. 在表单容器中使用

```typescript
import { useNLFormReaction } from './hooks/use-nl-form-reaction';
import { NLFormContext } from './nl-form-context';

const FormContainer: React.FC = () => {
  const [formValues, setFormValues] = useState({});
  const { reactionStates, getComponentsToClear, getComponentsToSetValue } = useNLFormReaction(
    formComponents,
    formValues
  );

  const handleFormValuesChange = useCallback((changedValues, allValues) => {
    setFormValues(allValues);
    
    // 处理清空值联动
    const componentsToClear = getComponentsToClear();
    if (componentsToClear.length > 0) {
      // 清空相关字段的值
    }
    
    // 处理设值联动
    const componentsToSetValue = getComponentsToSetValue();
    if (Object.keys(componentsToSetValue).length > 0) {
      // 设置相关字段的值
    }
  }, [getComponentsToClear, getComponentsToSetValue]);

  const contextValue = {
    formComponents,
    formValues,
    reactionStates,
    onFormValuesChange: handleFormValuesChange
  };

  return (
    <NLFormContext.Provider value={contextValue}>
      <Form onValuesChange={handleFormValuesChange}>
        {formComponents.map(component => {
          const state = reactionStates[component.uuid];
          if (state?.isHidden) return null;
          
          return <FormComponent key={component.uuid} nlFormComponent={component} />;
        })}
      </Form>
    </NLFormContext.Provider>
  );
};
```

## 联动逻辑说明

### 条件组合逻辑

1. **条件内部关系**: 使用 `conditionRelList` 定义条件间的关系（and/or）
2. **条件组间关系**: 使用 `conditionGroupRelList` 定义条件组间的关系（and/or）

### 执行优先级

1. 联动规则的结果优先于组件配置
2. `override_config` 的配置优先于联动规则
3. 隐藏状态会覆盖其他状态（隐藏的组件不会显示）

### 性能优化

- 使用 `useMemo` 缓存联动状态计算结果
- 只有相关字段值变化时才重新计算联动状态
- 支持调试模式，可以在开发环境中查看联动执行过程

## 调试

在开发环境中，可以通过以下方式调试联动功能：

```typescript
import { debugReaction } from './hooks/use-nl-form-reaction-utils';

// 在联动执行时打印调试信息
debugReaction(componentUuid, reactionType, conditionResults, finalResult);
```

## 注意事项

1. 确保联动规则中引用的 `formItemUuid` 存在于表单组件中
2. 避免创建循环联动（A影响B，B又影响A）
3. 复杂的联动规则可能影响性能，建议合理设计
4. 联动规则的变更需要重新渲染表单组件

## 示例

查看 `examples/form-reaction-example.tsx` 文件获取完整的使用示例。
