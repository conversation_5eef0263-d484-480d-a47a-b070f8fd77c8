import type React from 'react';
import { useState } from 'react';
import { Form } from 'antd-mobile';
import type { NLFormComponent } from '#/entity/process/nl-form-components';
import { useNLFormReaction } from '../../hooks/use-nl-form-reaction';

/**
 * 表单联动示例组件
 * 演示如何使用表单联动功能
 */
const FormReactionExample: React.FC = () => {
  const [form] = Form.useForm();
  const [formValues, setFormValues] = useState<Record<string, any>>({});

  // 模拟表单组件配置（基于你提供的数据结构）
  const mockFormComponents: NLFormComponent[] = [
    {
      uuid: '5a445d710ae24cd276062b0c84850838',
      key: 'org',
      label: '学校',
      handler: 'formselect',
      hasValue: true,
      type: 'form',
      category: 'basic',
      icon: 'tsfont-formselect',
      config: {
        isRequired: true,
        isHide: false,
        isDisabled: false,
        isMask: false,
        isReadOnly: false,
        dataSource: 'matrix',
        matrixUuid: 'f894427cc1c531f79da49605ef8b112f',
        mapping: {
          text: '7ad81c2e277c32ab8020eaeff69f91b9',
          value: '7ad81c2e277c32ab8020eaeff69f91b9'
        },
        sourceColumnList: [
          {
            expression: 'equal',
            defaultValue: ['学校'],
            valueList: ['学校'],
            column: '66cc3cbc443d31e3a6c4c696eb7415ea',
            type: 'input'
          }
        ]
      },
      reaction: {},
      override_config: {}
    },
    {
      uuid: 'b642aececd80e83ae583a38849d969a2',
      key: 'org_area',
      label: '校区',
      handler: 'formselect',
      hasValue: true,
      type: 'form',
      category: 'basic',
      icon: 'tsfont-formselect',
      config: {
        isRequired: true,
        isHide: true, // 默认隐藏
        isDisabled: false,
        isMask: false,
        isReadOnly: false,
        dataSource: 'matrix',
        matrixUuid: 'f894427cc1c531f79da49605ef8b112f',
        mapping: {
          text: '7ad81c2e277c32ab8020eaeff69f91b9',
          value: '7ad81c2e277c32ab8020eaeff69f91b9'
        }
      },
      reaction: {
        // 显示联动：当(学校不为空 AND 姓名不等于'b') AND 具体位置不为空时，显示校区字段
        display: {
          conditionGroupList: [
            {
              uuid: '8d98fe66e8bf4659abedc413d96158c6',
              conditionList: [
                {
                  uuid: '4f3cdb7497a74ec9940bcbecc8623a5a',
                  formItemUuid: '5a445d710ae24cd276062b0c84850838', // 学校
                  expression: 'isnotnull',
                  valueList: []
                },
                {
                  uuid: 'ed3d8e8ef21643c5a217659fc66651f2',
                  formItemUuid: 'b068931cc450442b63f5b3d276ea4297', // 姓名
                  expression: 'notequal',
                  valueList: ['b']
                }
              ],
              conditionRelList: ['and']
            },
            {
              uuid: 'f15e6b7f496e4163a12b2009a49f6ed8',
              conditionList: [
                {
                  uuid: 'aa26f8a0479f46e580c716a0cfdebc45',
                  formItemUuid: '4b82677b6c1408df4be21ada9a584fde', // 具体位置
                  expression: 'isnotnull',
                  valueList: []
                }
              ],
              conditionRelList: []
            }
          ],
          conditionGroupRelList: ['and']
        },
        // 必填联动：当学校不为空时，校区为必填
        required: {
          conditionGroupList: [
            {
              uuid: '8913a7acbf604773918f30648d7d10ed',
              conditionList: [
                {
                  uuid: 'ed7112a959904a74b74b714bdb6f4749',
                  formItemUuid: '5a445d710ae24cd276062b0c84850838', // 学校
                  expression: 'isnotnull',
                  valueList: []
                }
              ],
              conditionRelList: []
            }
          ],
          conditionGroupRelList: []
        },
        // 清空值联动：当学校改变或学校为空时，清空校区的值
        clearValue: {
          conditionGroupList: [
            {
              uuid: 'c5f7015ee7bf4cb9b1a8f0f6d98a327b',
              conditionList: [
                {
                  uuid: '67005c636443444b8ae280de94ed0bdf',
                  formItemUuid: '5a445d710ae24cd276062b0c84850838', // 学校
                  expression: 'change',
                  valueList: []
                }
              ],
              conditionRelList: []
            },
            {
              uuid: 'bc49b50511a940cbaf1fb23c3b97cff2',
              conditionList: [
                {
                  uuid: 'a2f5f166e8ce48cfb94b94585f64b860',
                  formItemUuid: '5a445d710ae24cd276062b0c84850838', // 学校
                  expression: 'isnull',
                  valueList: []
                }
              ],
              conditionRelList: []
            }
          ],
          conditionGroupRelList: ['or']
        }
      },
      override_config: {}
    },
    {
      uuid: '4b82677b6c1408df4be21ada9a584fde',
      key: 'area',
      label: '具体位置',
      handler: 'formtext',
      hasValue: true,
      type: 'form',
      category: 'basic',
      icon: 'tsfont-forminput',
      config: {
        isRequired: true,
        isHide: false,
        isDisabled: false,
        isMask: false,
        isReadOnly: false,
        placeholder: '请输入具体位置，如东校区-1号楼-302'
      },
      reaction: {},
      override_config: {}
    },
    {
      uuid: 'b068931cc450442b63f5b3d276ea4297',
      key: 'name',
      label: '姓名',
      handler: 'formtext',
      hasValue: true,
      type: 'form',
      category: 'basic',
      icon: 'tsfont-forminput',
      config: {
        isRequired: true,
        isHide: false,
        isDisabled: false,
        isMask: false,
        isReadOnly: false
      },
      reaction: {},
      override_config: {}
    }
  ];

  // 使用联动Hook
  const { reactionStates } = useNLFormReaction(mockFormComponents, formValues);

  // 处理表单值变化
  const handleValuesChange = (changedValues: any, allValues: any) => {
    console.log('表单值变化:', { changedValues, allValues });
    setFormValues(allValues);
  };

  return (
    <div className="p-4">
      <h2 className="text-lg font-bold mb-4">表单联动示例</h2>
      
      <div className="mb-4 p-3 bg-gray-100 rounded">
        <h3 className="font-semibold mb-2">联动规则说明：</h3>
        <ul className="text-sm space-y-1">
          <li>• 当学校不为空且姓名不等于'b'且具体位置不为空时，显示校区字段</li>
          <li>• 当学校不为空时，校区字段为必填</li>
          <li>• 当学校改变或为空时，清空校区的值</li>
        </ul>
      </div>

      <Form
        form={form}
        layout="vertical"
        onValuesChange={handleValuesChange}
      >
        {mockFormComponents.map(component => {
          const state = reactionStates[component.uuid];
          
          if (state?.isHidden) {
            return null;
          }

          return (
            <Form.Item
              key={component.uuid}
              name={component.uuid}
              label={component.label}
              required={state?.isRequired ?? component.config?.isRequired}
              hidden={state?.isHidden ?? component.config?.isHide}
            >
              <input 
                type="text" 
                placeholder={`请输入${component.label}`}
                disabled={state?.isDisabled ?? component.config?.isDisabled}
                readOnly={state?.isReadonly ?? component.config?.isReadOnly}
                className={`w-full p-2 border rounded ${
                  state?.isMasked ? 'blur-sm' : ''
                } ${
                  state?.isDisabled ? 'bg-gray-100' : ''
                }`}
              />
            </Form.Item>
          );
        })}
      </Form>

      <div className="mt-6 p-3 bg-blue-50 rounded">
        <h3 className="font-semibold mb-2">当前联动状态：</h3>
        <pre className="text-xs overflow-auto">
          {JSON.stringify(reactionStates, null, 2)}
        </pre>
      </div>

      <div className="mt-4 p-3 bg-green-50 rounded">
        <h3 className="font-semibold mb-2">当前表单值：</h3>
        <pre className="text-xs overflow-auto">
          {JSON.stringify(formValues, null, 2)}
        </pre>
      </div>
    </div>
  );
};

export default FormReactionExample;
