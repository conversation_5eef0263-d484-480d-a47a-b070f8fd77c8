import type { NLFormComponent, NLFormComponentMapping } from '#/entity/process/nl-form-components';
import { Form, type FormItemProps, type PickerRef } from 'antd-mobile';
import type React from 'react';
import classNames from 'classnames';
import { forwardRef, useMemo, useContext } from 'react';
import PopupCheckList from '@/components/popup-check-list';
import useNLMatrix from '../../hooks/use-nl-matrix';
import type { NLFormElement } from '../../types';
import { NLFormContext } from '../../nl-form-context';

interface NLFormSelectProps extends FormItemProps {
  nlFormComponent: NLFormComponent
}

export const SelectInput = forwardRef<PickerRef, NLFormElement & { options: NLFormComponentMapping[], isMultiple: boolean }>((props, ref) => {
  const { value, onChange, nlFormComponent, options, isMultiple } = props;
  const selectValue = (value?.dataList as NLFormComponentMapping[]) || []
  const popupCheckListValue = useMemo(() => selectValue.map((item) => item.value), [selectValue]);

  const handleSelectChange = (v: (string | number)[]) => {
    const newSelectValue = options.filter((item) => v.includes(item.value))
    const newValue = {
      attributeUuid: nlFormComponent.uuid,
      key: nlFormComponent.key,
      handler: nlFormComponent.handler || '',
      dataList: newSelectValue
    };
    onChange?.(newValue);
  };

  return (
    <PopupCheckList
      options={options}
      multiple={isMultiple}
      value={popupCheckListValue}
      onChange={handleSelectChange}
      ref={ref}
    />
  );
});

const NLFormSelect: React.FC<NLFormSelectProps> = (props) => {
  const { nlFormComponent, ...rest} = props
  const config = nlFormComponent.config;
  const dataSourceType = config.dataSource
  const staticDataList = config.dataList || []
  const isMultiple = config.isMultiple; // 是否多选
  const matrixData = useNLMatrix(config, nlFormComponent.key)

  // 获取联动状态
  const { reactionStates } = useContext(NLFormContext);
  const reactionState = reactionStates?.[nlFormComponent.uuid];
  
  const rules = useMemo(() => {
    const tempRules: FormItemProps['rules'] = [];
    // 使用联动状态的必填属性，如果没有联动状态则使用配置的必填属性
    const isRequired = reactionState?.isRequired ?? config.isRequired;

    if (isRequired) {
      tempRules.push({
        validator(_, value) {
          // 检查value是否存在且有dataList属性
          if (!value || !value.dataList || !Array.isArray(value.dataList) || value.dataList.length === 0) {
            return Promise.reject(new Error(`请选择${nlFormComponent.label}`));
          }
          return Promise.resolve();
        }
      })
    }
    return tempRules
  }, [reactionState?.isRequired, config.isRequired, nlFormComponent.label])
  
  // 处理默认值
  const defaultValue = useMemo(() => {
    const d = config.defaultValue
    if (!d) return []
    
    if (typeof d === 'string') {
      return [d]
    } 
    
    if (d?.value) {
      return [d]
    }
    
    return []
  }, [config.defaultValue])

  // 构建选项数据
  const options = useMemo(() => {
    let sourceData: NLFormComponentMapping[] = [];
    
    if (dataSourceType === 'static' && staticDataList) {
      sourceData = staticDataList
    } else if (dataSourceType === 'matrix' && matrixData) {
      sourceData = matrixData;
    }
    return sourceData
  }, [staticDataList, dataSourceType, matrixData])

  // console.log(reactionState, '())()', nlFormComponent.label);

  return (
    <Form.Item
      key={nlFormComponent.key}
      name={nlFormComponent.uuid}
      label={nlFormComponent.label}
      help={config.description}
      required={reactionState?.isRequired ?? config.isRequired}
      disabled={reactionState?.isDisabled ?? config.isDisabled}
      hidden={reactionState?.isHidden ?? config.isHide}
      initialValue={{
        attributeUuid: nlFormComponent.uuid,
        key: nlFormComponent.key,
        handler: nlFormComponent.handler || '',
        dataList: defaultValue || []
      }}
      rules={rules}
      onClick={(_, ref) => {
        ref.current?.open()
      }}
      {...rest}
      className={classNames({
        'blur-sm': reactionState?.isMasked ?? config.isMask,
        'pointer-events-none': reactionState?.isReadonly ?? config.isReadOnly
      })}
    >
      <SelectInput nlFormComponent={nlFormComponent} options={options} isMultiple={!!isMultiple} />
    </Form.Item>
  )
}

export default NLFormSelect;