import { Form, Image, ImageUploader, Input, TextArea, type FormProps, type ImageUploaderProps } from 'antd-mobile';
import type React from 'react';
import nlFormItemsMap, { NLSafetyFormItem } from './nl-form-items';
import useNLForm from './hooks/use-nl-form';
import { useEffect, useMemo, useState, useCallback, type PropsWithChildren } from 'react';
import useUpload from '@/hooks/use-upload';
import type { NLFormItemValue, TableList } from '#/entity/process';
import { useAuthStore } from '@/store/authStore';
import type { NLFormComponent } from '#/entity/process/nl-form-components';
import { useNLFormReaction } from './hooks/use-nl-form-reaction';
import { NLFormContext } from './nl-form-context';

function filterNlFormComponents(tableList?: TableList[]) {
  const nlFormComponentsForMobile: NLFormComponent[] = []
  // 移动端不渲染标签组件，还可以忽略管理端定义的表格结构
  tableList?.map((item) => {
    if (item.component && item.component.handler !== 'formlabel') {
      nlFormComponentsForMobile.push(item.component)
    }
  })
  return nlFormComponentsForMobile
}

interface NLFormCoreProps extends FormProps {
}

const ImagePreview: ImageUploaderProps['renderItem'] = (_, file) => {

  const apiBase = import.meta.env.VITE_APP_API_BASE_URL;
  const { token } = useAuthStore.getState();
  const donwloadUrl = `${apiBase}/v1/proxy/src/neatlogic/api/binary/file/download?id=${(file as any).id}&token=${token}`
  return <Image src={donwloadUrl} width="80px" height="80px" />
}

const Block: React.FC<PropsWithChildren> = ({children}) => (
  <div className='bg-white rounded-sm overflow-hidden'>
    {children}
  </div>
)

const NLFormCore: React.FC<NLFormCoreProps> = (props) => {
  const { form, draft } = useNLForm()
  const { upload } = useUpload()

  // 表单值状态
  const [formValues, setFormValues] = useState<Record<string, NLFormItemValue>>({});

  const nlFormComponents = useMemo(() => {
    const formConfig = draft?.formConfig;
    const currentSceneUuid = draft?.startProcessTaskStep.formSceneUuid || formConfig?.defaultSceneUuid
    const topNlFormComponents = filterNlFormComponents(formConfig?.tableList)
    if (formConfig?.uuid === currentSceneUuid) {
      return topNlFormComponents
    }
    const targetTableList = formConfig?.sceneList.find(item => item.uuid === currentSceneUuid)?.tableList;
    const targetTableListForMobile = filterNlFormComponents(targetTableList)
    const result: NLFormComponent[] = [];
    targetTableListForMobile.forEach((item) => {
      if (item.inherit) {
        const topComponent = topNlFormComponents.find(topItem => topItem.uuid === item.uuid)
        if (topComponent) {
          result.push(topComponent)
        }
      } else {
        result.push(item)
      }
    })
    return result

  }, [draft?.formConfig, draft?.startProcessTaskStep.formSceneUuid])

  // 联动逻辑
  const { reactionStates, getComponentsToClear } = useNLFormReaction(
    nlFormComponents || [],
    formValues
  );

  // 处理表单值变化
  const handleFormValuesChange = useCallback((changedValues: any, allValues: any) => {
    console.log('表单值变化:', { changedValues, allValues });
    // 更新表单值状态
    const newFormValues: Record<string, NLFormItemValue> = {};
    Object.keys(allValues).forEach(key => {
      if (allValues[key] && typeof allValues[key] === 'object' && 'attributeUuid' in allValues[key]) {
        newFormValues[key] = allValues[key];
      }
    });
    console.log('更新后的表单值:', newFormValues);
    setFormValues(newFormValues);

    // 处理联动清空值
    const componentsToClear = getComponentsToClear();
    console.log('需要清空的组件:', componentsToClear);
    if (componentsToClear.length > 0) {
      const clearedValues: any = {};
      componentsToClear.forEach(uuid => {
        const component = nlFormComponents?.find(c => c.uuid === uuid);
        if (component) {
          console.log(`清空组件: ${component.label} (${uuid})`);
          clearedValues[uuid] = {
            attributeUuid: uuid,
            key: component.key,
            handler: component.handler,
            dataList: []  // 使用空数组而不是null
          };
        }
      });
      console.log('执行清空操作:', clearedValues);
      form?.setFieldsValue(clearedValues);
    }

  }, [nlFormComponents, getComponentsToClear, form]);
  useEffect(() => {
    if (!draft) {
      return
    }
    const title = draft.title;
    const content = draft.startProcessTaskStep.comment?.content;
    const files = draft.startProcessTaskStep.comment?.fileList
    const formAttributeDataMap = draft.formAttributeDataMap;
    const formValues: Record<string, NLFormItemValue> = {}
    for(const k in formAttributeDataMap) {
      const targetNLComponent = nlFormComponents?.find(item => item.uuid === k)
      if (targetNLComponent) {
        formValues[k] = {
          dataList: formAttributeDataMap[k],
          attributeUuid: targetNLComponent.uuid,
          handler: targetNLComponent.handler,
          key: targetNLComponent.key,
        }
      }
    }
    form?.setFieldsValue({
      ...formValues,
      title: title,
      content: content,
      files: files
    })
  }, [draft, form, nlFormComponents])

  if (!form) {
    return null
  }
  // 创建上下文值
  const contextValue = useMemo(() => ({
    form,
    processInfo: {
      channelPath: '',
      color: '',
      description: '',
      icon: '',
      channelUuid: '',
      priorityUuid: '',
    },
    draft,
    formComponents: nlFormComponents,
    formValues,
    reactionStates,
    onFormValuesChange: handleFormValuesChange
  }), [form, draft, nlFormComponents, formValues, reactionStates, handleFormValuesChange]);

  console.log(reactionStates);
  return (
    <NLFormContext.Provider value={contextValue}>
      <Form
        layout='vertical'
        form={form}
        onValuesChange={handleFormValuesChange}
        {...props}
      >
        <Block>
          <Form.Item
            label="标题"
            name="title"
            rules={[
              { required: true}
            ]}
          >
            <Input />
          </Form.Item>
        </Block>

        <Block>
          {
            nlFormComponents?.map((item) => {
              const Component = nlFormItemsMap[item.handler] || NLSafetyFormItem
              const componentReactionState = reactionStates[item.uuid];

              // 如果组件被隐藏，则不渲染
              if (componentReactionState?.isHidden) {
                return null;
              }

              return <Component key={item.uuid} nlFormComponent={item} />
            })
          }
        </Block>

        <Block>
          <Form.Item
            label="描述"
            name="content"
          >
            <TextArea />
          </Form.Item>
        </Block>
        <Block>
          <Form.Item
            label="附件"
            name="files"
          >
            <ImageUploader renderItem={ImagePreview} upload={upload} multiple />
          </Form.Item>
        </Block>

      </Form>
    </NLFormContext.Provider>
  )
}

export default NLFormCore;
